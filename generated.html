<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
            color: #333;
            font-size: 16px;
            --accent-color: #F9A82F;
            --background-color: #F9A82F3c;
        }

        h2 {
            font-size: 20px;
            margin: 0;
            margin-bottom: 4px;
        }

        p {
            margin: 0;
            line-height: 1.2;
            font-size: 14px;
        }

        /* Make empty paragraphs create proper spacing */
        p:empty {
            height: 16px;
            display: block;
        }

        .invoice-container {
            max-width: 800px;
            margin: auto 32px;
            background: white;
            padding: 32px;
            position: relative;
        }

        .logo {
            max-height: 88px;
            max-width: 40%;
            width: auto;
            margin-left: 16px;
            flex-shrink: 0;
        }

        .invoice-header,
        .client-details {
            margin-bottom: 20px;
        }

        .invoice-header h1 {
            color: var(--accent-color);
            font-size: 26px;
            margin: 24px 0px 16px 0;
        }

        .invoice-header p {
            margin: 0;
            line-height: 24px;
        }

        .client-details p {
            margin: 0;
            line-height: 24px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 24px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        /* First column gets remaining width with text wrapping */
        .items-table th:nth-child(1),
        .items-table td:nth-child(1) {
            width: 100%;
        }

        /* Other columns shrink to fit their content */
        .items-table th:nth-child(2),
        .items-table td:nth-child(2),
        .items-table th:nth-child(3),
        .items-table td:nth-child(3),
        .items-table th:nth-child(4),
        .items-table td:nth-child(4),
        .items-table th:nth-child(5),
        .items-table td:nth-child(5),
        .items-table th:nth-child(6),
        .items-table td:nth-child(6) {
            width: 1%;
        }

        /* Column-specific text alignment */
        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
            text-align: center;
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3),
        .items-table th:nth-child(4),
        .items-table td:nth-child(4),
        .items-table th:nth-child(5),
        .items-table td:nth-child(5),
        .items-table th:nth-child(6),
        .items-table td:nth-child(6) {
            text-align: right;
        }

        .items-table th {
            background-color: #f8f8f8;
        }

        .summary-line {
            text-align: right;
            font-size: 16px;
            font-weight: bold;
            border-top: 3px solid var(--accent-color);
            ;
        }

        .final-amount {
            font-weight: bold;
            font-size: 24px;
            background-color: var(--background-color);
            padding: 8px 16px;
            text-align: right;
            border-radius: 8px;
            margin-top: 32px;
        }

        .refunded {
            border: 2px solid var(--background-color);
            padding: 12px 14px;
            text-align: right;
            border-radius: 8px;
            margin-top: 16px;
        }

        .refunded p {
            margin: 0;
            line-height: 1.2;
        }

        .refunded p:first-child {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 4px;
        }

        .top-line-container {
            background-color: white;
            padding: 4px 16px;
            border: 3px solid var(--accent-color);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
        }

        .invoice-details {
            flex: 1;
        }

        .top-line {
            font-weight: bold;
            font-size: 12px;
            line-height: 1.5;
            margin: 4px 0;
        }

        .coupon {
            font-weight: bold;
            font-size: 12px;
            background-color: var(--background-color);
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
            position: relative;
            bottom: 2px;
        }

        .discount-code {
            border: 1px solid var(--accent-color);
            font-size: 10px;
            font-weight: bold;
            border-radius: 8px;
            padding: 4px 8px;
            display: inline-block;
            margin-left: 8px;
            background-color: var(--background-color);
        }

        a {
            color: #666;
            /* Slightly lighter than the regular text color (#333) */
            text-decoration: underline;
            text-decoration-color: var(--accent-color);
            text-decoration-thickness: 2px;
            text-underline-offset: 2px;
            /* Space between text and underline */
        }

        .spacer {
            height: 16px;
            display: block;
        }

        .cancelled-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            pointer-events: none;
        }

        .cancelled-text {
            font-size: 120px;
            font-weight: bold;
            color: rgba(220, 53, 69, 0.6);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            transform: rotate(-45deg);
            user-select: none;
            letter-spacing: 8px;
        }

        @media (max-width: 600px) {
            .invoice-container {
                width: 100%;
                margin: 0;
                padding: 32px 16px;
                box-sizing: border-box;
            }

            .cancelled-text {
                font-size: 60px;
                letter-spacing: 4px;
            }

            .items-table {
                font-size: 11px;
                width: 100%;
            }

            .items-table th,
            .items-table td {
                font-size: 11px;
                padding: 6px 4px;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .top-line-container {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .logo {
                margin-left: 0;
                margin-bottom: 12px;
                order: -1;
                max-width: 80%;
            }

            .top-line {
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <!-- Cancelled overlay - remove this div to hide the overlay -->
        <div class="cancelled-overlay">
            <div class="cancelled-text">CANCELLED</div>
        </div>

        <div class="top-line-container">
            <div class="invoice-details">
                <p class="top-line">RECEIPT: FREC-25-00048</p>
                <p class="top-line">DATE: 2025-07-23</p>
                <p class="top-line">PAYMENT METHOD: CASH</p>
            </div>
            <img class="logo" src="https://melopay-assets.b-cdn.net/project-logo/string-systems-logo-lm-opaque.png"
                alt="String Systems">
        </div>
        <header class="invoice-header">
            <p class="spacer"></p>
            <p class="spacer"></p>
            <p class="spacer"></p>
            <h2>String Systems</h2>
            <p>Nikolaou Skoufa 29</p>
            <p>Limassol 3016, Cyprus</p>
            <p>VAT number: CY10399982P</p>
            <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Tel: <a href="tel:+35799302141">(357) 99302141</a></p>
        </header>
        <section class="client-details">
            <h2>Paid by:</h2>
            <p>Patatarium Coorporation</p>
            <p>Kokkinoxorka</p>
            <p>Tax Id 7687867</p>
            <p>VAT ID: 12325445</p>
            <p class="spacer"></p>
        </section>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>#</th>
                    <th>Price</th>
                    <th>Tax</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Angourakia</td>
                    <td>7</td>
                    <td>€1.34</td>
                    <td style="white-space: nowrap; font-size: 10px;">VAT 19%%</td>
                    <td>€9.38</td>
                </tr>
                <tr>
                    <td>Kati allo</td>
                    <td>1</td>
                    <td>€30.00</td>
                    <td style="white-space: nowrap; font-size: 10px;">EXEMPT%</td>
                    <td>€30.00</td>
                </tr>
                <tr>
                    <td>And more</td>
                    <td>2</td>
                    <td>€42.00</td>
                    <td style="white-space: nowrap; font-size: 10px;">REVERSE CHARGE%</td>
                    <td>€84.00</td>
                </tr>
                <tr style="border-top: 3px solid #ddd;">
                    <td>Discount</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>(€23.38)</td>
                </tr>
                <tr class="summary-line">
                    <td>Total</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>€100.00</td>
                </tr>
            </tbody>
        </table>
        <p class="spacer"></p>
        <p style="font-weight: bold; font-size: 10px; margin-left: 12px;">ALL TAXES INCLUDED</p>
        <section>
            <p class="spacer"></p>
            <p class="final-amount">Paid: €100.00</p>
        </section>
    </div>
</body>

</html>