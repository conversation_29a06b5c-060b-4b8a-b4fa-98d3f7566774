<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
            color: #333;
            font-size: 16px;
            --accent-color: #F9A82E;
            --background-color: #F9A82E3c;
        }

        h1 {
            margin: 24px 0 8px 0;
        }

        h2 {
            font-size: 18px;
            margin: 0;
            margin-bottom: 8px;
        }

        p {
            margin: 0;
            line-height: 1.2;
        }

        /* Make empty paragraphs create proper spacing */
        p:empty {
            height: 16px;
            display: block;
        }

        .invoice-container {
            max-width: 800px;
            margin: auto 32px;
            background: white;
            padding: 24px;
        }

        .logo {
            max-height: 88px;
            max-width: 40%;
            width: auto;
            margin-left: 16px;
            flex-shrink: 0;
        }

        .invoice-header,
        .client-details,
        .summary {
            margin-bottom: 20px;
        }

        .notes {
            margin-top: 24px;
            text-align: center;
            border: 1px solid var(--accent-color);
            padding: 48px 24px 24px 24px;
            border-radius: 8px;
        }

        .invoice-header h1 {
            color: var(--accent-color);
            font-size: 24px;
        }

        .invoice-header p {
            margin: 0;
            line-height: 24px;
        }

        .client-details p {
            margin: 0;
            line-height: 24px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 24px;
            margin-top: 24px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        /* Column-specific text alignment */
        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
            text-align: center;
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3),
        .items-table th:nth-child(4),
        .items-table td:nth-child(4) {
            text-align: right;
        }

        .items-table th {
            background-color: #f8f8f8;
        }

        .summary p {
            margin: 10px 0;
        }

        .final-amount {
            font-weight: bold;
            font-size: 24px;
            background-color: var(--background-color);
            padding: 8px 16px;
            text-align: right;
            border-radius: 8px;
            margin-top: 16px;
        }

        .top-line-container {
            background-color: white;
            padding: 4px 16px;
            border: 3px solid var(--accent-color);
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
        }

        .invoice-details {
            flex: 1;
        }

        .top-line {
            font-weight: bold;
            font-size: 12px;
            line-height: 1.5;
            /* Increased from 8px to provide proper spacing */
            margin: 4px 0;
            /* Add vertical margin between lines */
        }

        .discount-code {
            border: 1px solid var(--accent-color);
            font-size: 10px;
            font-weight: bold;
            border-radius: 8px;
            padding: 4px 8px;
            display: inline-block;
            margin-left: 8px;
            background-color: var(--background-color);
        }

        a {
            color: #666;
            /* Slightly lighter than the regular text color (#333) */
            text-decoration: underline;
            text-decoration-color: var(--accent-color);
            text-decoration-thickness: 2px;
            text-underline-offset: 2px;
            /* Space between text and underline */
        }

        /* Class for adding vertical spacing */
        .spacer {
            height: 16px;
            display: block;
        }

        /* Payment button styling */
        .payment-link {
            text-align: center;
            margin: 30px 0;
        }

        .payment-link a {
            display: inline-block;
            background-color: var(--accent-color);
            color: white;
            font-weight: bold;
            padding: 16px 32px;
            margin: 16px;
            font-size: 20px;
            border-radius: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-decoration: none;
            /* Override the general link styling */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 600px) {
            .invoice-container {
                width: 100%;
                margin: 0;
                padding: 16px;
                box-sizing: border-box;
            }

            .items-table {
                font-size: 11px;
                table-layout: fixed;
                width: 100%;
            }

            .items-table th,
            .items-table td {
                font-size: 11px;
                padding: 6px 4px;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            /* Adjust column widths for mobile */
            .items-table th:nth-child(1),
            .items-table td:nth-child(1) {
                width: 40%;
            }

            .items-table th:nth-child(2),
            .items-table td:nth-child(2) {
                width: 15%;
            }

            .items-table th:nth-child(3),
            .items-table td:nth-child(3) {
                width: 20%;
            }

            .items-table th:nth-child(4),
            .items-table td:nth-child(4) {
                width: 25%;
            }

            .top-line-container {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .logo {
                margin-left: 0;
                margin-bottom: 12px;
                order: -1;
                max-width: 80%;
            }

            .summary {
                text-align: center !important;
            }

            .summary p {
                text-align: center !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            .top-line,
            .final-amount {
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <div class="top-line-container">
            <div class="invoice-details">
                <p class="top-line">INVOICE: INV-{* INVOICE_PREFIX *}-{* INVOICE_NUMBER *}</p>
                <p class="top-line">DATE: {* INVOICE_DATE *}</p>
                <p class="top-line">DUE DATE: {* INVOICE_DUE_DATE *}</p>
            </div>
            <img class="logo" src="https://melopay-assets.b-cdn.net/project-logo/string-systems-logo-lm-opaque.png"
                alt="{* BUSINESS_NAME *}">
        </div>
        <header class="invoice-header">
            <h1>StringWorks Digital Studio</h1>
            <p>String Systems aka Effective Music Practice LTD</p>
            <p>Nikolaou Skoufa 29,</p>
            <p>Limassol 3016, Cyprus</p>
            <p>VAT ID: 10399982P</p>
            <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>Tel: <a href="tel:">(357) 99302141</a></p>
        </header>
        <section class="client-details">
            <h2>Bill To:</h2>
            <p>{* BILLTO_BUSINESS_NAME_OR_CUSTOMER_NAME *}</p>
            <p>{* BILLTO_ADDRESS_LINE_1 *}</p>
            <p>{* BILLTO_ADDRESS_LINE_2 *}</p>
            <p>{* BILLTO_TAX_ID *}</p>
            <p>Email: <a href="mailto:{* BILLTO_EMAIL *}">{* BILLTO_EMAIL *}</a></p>
            <p>Tel: <a href="tel:{* BILLTO_TEL *}">{* BILLTO_TEL *}</a></p>
        </section>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item Description</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Website Design</td>
                    <td>1</td>
                    <td>€400.00</td>
                    <td>€400.00</td>
                </tr>
                <tr>
                    <td>Website Development</td>
                    <td>1</td>
                    <td>€100.00</td>
                    <td>€100.00</td>
                </tr>
                <tr>
                    <td>Email Integration</td>
                    <td>1</td>
                    <td>€50.00</td>
                    <td>€50.00</td>
                </tr>
                <tr>
                    <td>SEO and Publishing - Single Page Website</td>
                    <td>1</td>
                    <td>€20.00</td>
                    <td>€20.00</td>
                </tr>
                <tr>
                    <td>Discount <span class="discount-code"> {* DISCOUNT_CODE *}</span></td>
                    <td></td>
                    <td>€359.92</td>
                    <td>(€359.92)</td>
                </tr>
            </tbody>
        </table>
        <section class="summary">
            <p style="text-align: right; font-size: 18px; font-weight: bold; margin-right: 16px;">Subtotal: €210.08</p>
            <p style="text-align: right; font-size: 18px; font-weight: bold; margin-right: 16px;">VAT(19%): 39.92</p>
            <p class="spacer"></p>
            <p class="final-amount">Total: €250.00</p>
        </section>
        <p class="payment-link"><a target="_blank" href="HTTPS://STRING.SYSTEMS">CLICK HERE TO PAY ONLINE</a></p>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <div class="spacer"></div>
        <section class="notes">
            <p>
            <p>Details for payment by bank transfer:</p>
            <p></p>
            <p>Prokopis Skordis</p>
            <p>Bank of Cyprus Account Number: ************</p>
            <p>IBAN: <strong>****************************</strong></p>
            <p>Description: Use the <strong>invoice number</strong> at the top of this page.</p>
            <p></p>
            <p>Thank you for your business. It’s a pleasure to work with you on your project.</p>
            <p></p>
            <p>Yours sincerely,</p>
            <p>Prokopis Skordis</p>
            <p></p>
        </section>
    </div>
</body>

</html>